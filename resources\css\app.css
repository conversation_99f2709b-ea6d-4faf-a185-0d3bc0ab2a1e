@tailwind base;
@tailwind components;
@tailwind utilities;

[x-cloak] {
    display: none !important;
}


/* Info-bulle */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 140px;
    background-color: #555;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 10;
    top: 50%;
    right: 110%; /* Doit s'ouvrir à gauche de l'icone */
    transform: translateY(-50%);
    opacity: 0;
    transition: opacity 0.3s;
}

/* Forme triangle => Flèche de l'info-bulle */
.tooltip .tooltiptext::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 100%;
    margin-top: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: transparent transparent transparent #555;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}


/* Styles pour les sliders /////////////////////////////////////*/

/* Pour duration-filter et price-filter */
.slider::-webkit-slider-thumb {
    appearance: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #10b981;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #10b981;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Pour les étoiles actives */
.star.active {
    color: #fbbf24 !important;
}

/* Pour masquer les cards filtrées */
.covoiturage-card.filtered-out {
    display: none !important;
}
